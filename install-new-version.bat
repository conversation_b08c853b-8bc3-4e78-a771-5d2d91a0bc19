@echo off
echo ========================================
echo    安装新版本蓝牙Hook模块 v2.1-NameSupport
echo ========================================

echo.
echo 正在卸载旧版本...
adb uninstall com.example.bluetoothscanner

echo.
echo 正在安装新版本...
adb install app\build\outputs\apk\debug\app-debug.apk

echo.
echo 正在复制配置文件...
adb push bluetooth_hook_config.txt /sdcard/

echo.
echo ========================================
echo 安装完成！
echo.
echo 接下来请手动操作：
echo 1. 在LSPosed管理器中重新激活模块
echo 2. 确保勾选 com.android.bluetooth 进程
echo 3. 重启手机
echo 4. 查看日志确认新版本已加载
echo    (应该看到: "极简安全蓝牙Hook启动 v2.1-NameSupport")
echo ========================================

pause
