package com.example.bluetoothscanner;

import android.bluetooth.BluetoothDevice;
import android.content.Intent;

import java.io.File;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;

/**
 * 极简安全蓝牙Hook - 只监听广播，绝不干扰蓝牙功能
 */
public class SimpleBluetoothHook {
    private static final String TAG = "BluetoothHook";
    private static final String VERSION = "v2.1-NameSupport"; // 版本标识

    // 配置参数 - 从SharedPreferences读取
    private static String TARGET_MAC = "61:14:F1:05:88:00"; // 用户指定的MAC地址
    private static String TARGET_NAME = ""; // 用户指定的设备名称
    private static final int MIN_RSSI = -80;
    private static final int MAX_RSSI = -20;
    private static final String CURL_APPEAR = "curl -X POST https://httpbin.org/post -d 'device_appear=true'";
    private static final String CURL_DISAPPEAR = "curl -X POST https://httpbin.org/post -d 'device_disappear=true'";

    // SharedPreferences相关
    private static final String PREFS_NAME = "bluetooth_hook_config";
    private static final String KEY_TARGET_MAC = "target_mac";
    private static final String KEY_TARGET_NAME = "target_name";
    
    // 日志相关 - 简化为只使用Xposed日志
    private static final SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
    private static final String SIMPLE_LOG_FILE = "/sdcard/bluetooth_hook_simple.log";
    
    // 设备状态
    private static final Map<String, Boolean> devicePresent = new HashMap<>();
    private static final Map<String, Long> lastLogTime = new HashMap<>();

    // 监控状态
    private static boolean lastReportedPresent = false;
    private static long lastStatusReport = 0;
    
    /**
     * 读取配置 - 使用多种方式
     */
    private static void loadConfig() {
        try {
            // 尝试多个可能的配置文件路径
            String[] configPaths = {
                "/data/data/com.example.bluetoothscanner/shared_prefs/" + PREFS_NAME + ".xml",
                "/storage/emulated/0/Android/data/com.example.bluetoothscanner/files/config.txt",
                "/sdcard/bluetooth_hook_config.txt",
                "/data/local/tmp/bluetooth_hook_config.txt"
            };

            boolean configLoaded = false;
            log("🔍 开始搜索配置文件...");

            for (String configPath : configPaths) {
                java.io.File configFile = new java.io.File(configPath);
                log("📁 检查配置文件: " + configPath + " (存在: " + configFile.exists() + ")");

                if (configFile.exists()) {
                    try {
                        log("📖 尝试读取配置文件: " + configPath);
                        if (configPath.endsWith(".xml")) {
                            // XML格式解析
                            configLoaded = loadFromXml(configFile);
                        } else {
                            // 简单文本格式解析
                            configLoaded = loadFromText(configFile);
                        }

                        if (configLoaded) {
                            String targetInfo = "";
                            if (!TARGET_MAC.isEmpty()) targetInfo += "MAC: " + TARGET_MAC;
                            if (!TARGET_NAME.isEmpty()) {
                                if (!targetInfo.isEmpty()) targetInfo += ", ";
                                targetInfo += "名称: " + TARGET_NAME;
                            }
                            log("✅ 已从配置文件加载目标设备 - " + targetInfo + " (路径: " + configPath + ")");
                            break;
                        } else {
                            log("⚠️ 配置文件存在但解析失败: " + configPath);
                        }
                    } catch (Exception e) {
                        log("⚠️ 读取配置文件失败: " + configPath + " - " + e.getMessage());
                    }
                }
            }

            if (!configLoaded) {
                String defaultInfo = "MAC: " + TARGET_MAC;
                if (!TARGET_NAME.isEmpty()) defaultInfo += ", 名称: " + TARGET_NAME;
                log("📝 未找到配置文件，使用默认目标: " + defaultInfo);
                // 尝试创建简单配置文件
                createSimpleConfig();
            }

        } catch (Exception e) {
            log("❌ 读取配置失败: " + e.getMessage() + "，使用默认MAC: " + TARGET_MAC);
        }
    }

    /**
     * 从XML文件加载配置
     */
    private static boolean loadFromXml(java.io.File configFile) {
        boolean foundMac = false;
        boolean foundName = false;

        try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(configFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 解析MAC地址
                if (line.contains("name=\"" + KEY_TARGET_MAC + "\"")) {
                    int start = line.indexOf("value=\"") + 7;
                    int end = line.indexOf("\"", start);
                    if (start > 6 && end > start) {
                        String newMac = line.substring(start, end);
                        if (isValidMacAddress(newMac)) {
                            TARGET_MAC = newMac;
                            foundMac = true;
                        } else {
                            log("⚠️ 配置中的MAC地址格式无效: " + newMac);
                        }
                    }
                }
                // 解析设备名称
                else if (line.contains("name=\"" + KEY_TARGET_NAME + "\"")) {
                    int start = line.indexOf("value=\"") + 7;
                    int end = line.indexOf("\"", start);
                    if (start > 6 && end > start) {
                        String newName = line.substring(start, end).trim();
                        if (!newName.isEmpty()) {
                            TARGET_NAME = newName;
                            foundName = true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log("❌ XML配置解析失败: " + e.getMessage());
        }

        // 至少需要MAC或名称其中之一
        return foundMac || foundName;
    }

    /**
     * 从文本文件加载配置
     */
    private static boolean loadFromText(java.io.File configFile) {
        boolean foundMac = false;
        boolean foundName = false;

        try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(configFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.startsWith("#") || line.isEmpty()) {
                    continue; // 跳过注释和空行
                }

                if (line.startsWith("target_mac=")) {
                    String newMac = line.substring(11).trim();
                    if (isValidMacAddress(newMac)) {
                        TARGET_MAC = newMac;
                        foundMac = true;
                    } else {
                        log("⚠️ 配置中的MAC地址格式无效: " + newMac);
                    }
                }
                else if (line.startsWith("target_name=")) {
                    String newName = line.substring(12).trim();
                    if (!newName.isEmpty()) {
                        TARGET_NAME = newName;
                        foundName = true;
                    }
                }
            }
        } catch (Exception e) {
            log("❌ 文本配置解析失败: " + e.getMessage());
        }

        // 至少需要MAC或名称其中之一
        return foundMac || foundName;
    }

    /**
     * 创建简单配置文件
     */
    private static void createSimpleConfig() {
        try {
            java.io.File configFile = new java.io.File("/sdcard/bluetooth_hook_config.txt");
            try (java.io.FileWriter writer = new java.io.FileWriter(configFile)) {
                writer.write("# 蓝牙Hook配置文件\n");
                writer.write("# 修改配置后重启手机生效\n");
                writer.write("# 可以使用MAC地址或设备名称，或者两者都使用\n");
                writer.write("# MAC地址格式: AA:BB:CC:DD:EE:FF\n");
                writer.write("# 设备名称: 任意字符串\n");
                writer.write("\n");
                writer.write("target_mac=" + TARGET_MAC + "\n");
                writer.write("target_name=" + TARGET_NAME + "\n");
                writer.write("\n");
                writer.write("# 示例:\n");
                writer.write("# target_mac=CF:DD:17:EC:0F\n");
                writer.write("# target_name=My Device\n");
                writer.flush();
            }
            log("📝 已创建配置文件: /sdcard/bluetooth_hook_config.txt");
        } catch (Exception e) {
            // 静默忽略创建失败
        }
    }

    /**
     * 验证MAC地址格式
     */
    private static boolean isValidMacAddress(String mac) {
        if (mac == null || mac.length() != 17) return false;
        return mac.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$");
    }

    /**
     * 检查设备是否匹配目标（MAC地址或设备名称）
     */
    private static boolean isTargetDevice(String mac, String name) {
        // 检查MAC地址匹配
        boolean macMatch = false;
        if (!TARGET_MAC.isEmpty() && mac != null) {
            macMatch = TARGET_MAC.equalsIgnoreCase(mac);
        }

        // 检查设备名称匹配
        boolean nameMatch = false;
        if (!TARGET_NAME.isEmpty() && name != null) {
            nameMatch = name.toLowerCase().contains(TARGET_NAME.toLowerCase());
        }

        // 如果两个条件都设置了，需要都匹配；如果只设置了一个，匹配一个即可
        if (!TARGET_MAC.isEmpty() && !TARGET_NAME.isEmpty()) {
            return macMatch && nameMatch; // 都必须匹配
        } else {
            return macMatch || nameMatch; // 任一匹配即可
        }
    }

    /**
     * 获取目标设备的描述信息
     */
    private static String getTargetDescription() {
        StringBuilder desc = new StringBuilder();
        if (!TARGET_MAC.isEmpty()) {
            desc.append("MAC: ").append(TARGET_MAC);
        }
        if (!TARGET_NAME.isEmpty()) {
            if (desc.length() > 0) desc.append(", ");
            desc.append("名称: ").append(TARGET_NAME);
        }
        return desc.toString();
    }

    /**
     * 初始化Hook - 极简安全版本
     */
    public static void init(ClassLoader classLoader, String packageName) {
        XposedBridge.log(TAG + ": ========== 极简安全蓝牙Hook启动 " + VERSION + " ==========");
        XposedBridge.log(TAG + ": 📦 进程: " + packageName);

        // 加载配置
        loadConfig();

        String targetDesc = getTargetDescription();
        XposedBridge.log(TAG + ": 🎯 目标设备: " + targetDesc);
        XposedBridge.log(TAG + ": 📶 RSSI范围: [" + MIN_RSSI + ", " + MAX_RSSI + "]");

        log("========== 极简安全蓝牙Hook启动 ==========");
        log("📦 进程: " + packageName);
        log("🎯 目标设备: " + targetDesc);
        log("📶 RSSI范围: [" + MIN_RSSI + ", " + MAX_RSSI + "] dBm");
        
        // 只安装最安全的广播Hook
        boolean hookSuccess = hookBluetoothBroadcastOnly(classLoader);
        
        if (hookSuccess) {
            log("✅ 极简Hook安装成功 - 只监听广播，绝不干扰蓝牙");
            startMonitor();
        } else {
            log("❌ Hook安装失败");
        }
        
        log("========== Hook初始化完成 ==========");
        XposedBridge.log(TAG + ": ========== Hook初始化完成 ==========");
    }
    
    /**
     * 极简蓝牙广播Hook - 只监控目标MAC地址
     */
    private static boolean hookBluetoothBroadcastOnly(ClassLoader classLoader) {
        boolean success = false;

        log("🎯 开始安装Hook，目标设备: " + getTargetDescription());

        // 尝试Hook多个可能的实现类
        String[] contextImplClasses = {
            "android.app.ContextImpl",
            "android.content.ContextWrapper"
        };

        for (String className : contextImplClasses) {
            try {
                Class<?> contextImplClass = XposedHelpers.findClass(className, classLoader);
                XposedHelpers.findAndHookMethod(contextImplClass, "sendBroadcast", Intent.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        // 在广播发送后监听，绝不阻止或修改广播
                        try {
                            Intent intent = (Intent) param.args[0];
                            if (intent != null && BluetoothDevice.ACTION_FOUND.equals(intent.getAction())) {
                                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                                if (device != null) {
                                    String mac = device.getAddress();
                                    String name = device.getName();

                                    // 检查是否为目标设备（MAC或名称匹配）
                                    if (isTargetDevice(mac, name)) {
                                        int rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE);

                                        log("🎯 [广播] 发现目标设备: " + name + " (" + mac + "), RSSI: " + rssi + " dBm");
                                        checkDevice(mac, name, rssi);
                                    }
                                    // 对于非目标设备，完全静默，不记录任何日志
                                }
                            }
                        } catch (Exception e) {
                            // 静默忽略所有错误，绝不影响广播系统
                        }
                    }
                });

                log("✅ " + className + " 广播Hook已安装 (监控目标: " + getTargetDescription() + ")");
                success = true;

            } catch (Exception e) {
                log("❌ " + className + " Hook失败: " + e.getMessage());
            }
        }

        // 简化BluetoothAdapter Hook，只监控关键扫描事件
        try {
            Class<?> adapterClass = XposedHelpers.findClass("android.bluetooth.BluetoothAdapter", classLoader);

            // 只Hook startDiscovery来确认扫描开始
            XposedHelpers.findAndHookMethod(adapterClass, "startDiscovery", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    boolean result = (Boolean) param.getResult();
                    if (result) {
                        log("🔍 蓝牙扫描已启动，正在寻找目标设备: " + getTargetDescription());
                    }
                }
            });

            log("✅ BluetoothAdapter Hook已安装 (目标: " + getTargetDescription() + ")");
            success = true;

        } catch (Exception e) {
            log("❌ BluetoothAdapter Hook失败: " + e.getMessage());
        }

        // 完全移除BluetoothDevice的Hook，避免产生大量日志
        // 只依靠广播Hook来检测目标设备
        log("ℹ️ 跳过BluetoothDevice Hook以减少日志噪音");

        return success;
    }
    
    /**
     * 检查设备
     */
    private static void checkDevice(String mac, String name, int rssi) {
        try {
            boolean inRange = (rssi >= MIN_RSSI && rssi <= MAX_RSSI);
            boolean wasPresent = devicePresent.getOrDefault(mac, false);
            
            String timestamp = timeFormat.format(new Date());
            log("🔍 [" + timestamp + "] 检查设备: " + name + " (" + mac + ")");
            log("📶 RSSI: " + rssi + " dBm (范围: " + MIN_RSSI + " 到 " + MAX_RSSI + ")");
            log("✅ 在范围内: " + inRange + " | 之前存在: " + wasPresent);
            
            if (inRange && !wasPresent) {
                // 设备进入范围
                devicePresent.put(mac, true);
                log("🟢 ✅ 目标设备进入信号范围!");
                log("📍 设备: " + name + " (" + mac + ")");
                log("📶 信号强度: " + rssi + " dBm");
                executeCurl(CURL_APPEAR, "设备出现");
                
            } else if (!inRange && wasPresent) {
                // 设备离开范围
                devicePresent.put(mac, false);
                log("🔴 ❌ 目标设备离开信号范围!");
                log("📍 设备: " + name + " (" + mac + ")");
                log("📶 信号强度: " + rssi + " dBm");
                executeCurl(CURL_DISAPPEAR, "设备消失");
                
            } else if (inRange && wasPresent) {
                log("📍 目标设备持续在范围内: " + rssi + " dBm");
            } else {
                String reason = rssi < MIN_RSSI ? "信号太弱" : "信号太强";
                log("📶 目标设备不在范围: " + rssi + " dBm (" + reason + ")");
            }
            
        } catch (Exception e) {
            log("❌ 检查设备错误: " + e.getMessage());
        }
    }
    
    /**
     * 执行curl命令
     */
    private static void executeCurl(String command, String reason) {
        new Thread(() -> {
            try {
                log("🌐 执行curl (" + reason + "): " + command);
                
                Process process = Runtime.getRuntime().exec(new String[]{"sh", "-c", command});
                int exitCode = process.waitFor();
                
                if (exitCode == 0) {
                    log("✅ curl执行成功 (" + reason + ")");
                } else {
                    log("❌ curl执行失败 (" + reason + "), 退出码: " + exitCode);
                }
                
            } catch (Exception e) {
                log("❌ curl执行异常 (" + reason + "): " + e.getMessage());
            }
        }, "CurlExecutor").start();
    }
    
    /**
     * 启动监控线程
     */
    private static void startMonitor() {
        new Thread(() -> {
            try {
                Thread.sleep(10000); // 等待10秒
                log("🔍 监控线程启动 - 专门监控目标设备: " + getTargetDescription());

                while (true) {
                    try {
                        // 只显示目标设备状态 (使用MAC作为key，因为MAC是唯一的)
                        String deviceKey = !TARGET_MAC.isEmpty() ? TARGET_MAC : "TARGET_DEVICE";
                        boolean targetPresent = devicePresent.getOrDefault(deviceKey, false);
                        boolean targetDetected = lastLogTime.containsKey(deviceKey);

                        // 只在状态变化时记录日志，减少噪音
                        if (targetDetected) {
                            // 只在第一次检测到或状态变化时记录
                            if (targetPresent != lastReportedPresent) {
                                log("🎯 目标设备 " + getTargetDescription() + " 状态变化: " + (targetPresent ? "✅ 进入信号范围" : "❌ 离开信号范围"));
                                lastReportedPresent = targetPresent;
                            }
                        } else {
                            // 每5分钟报告一次监控状态
                            long currentTime = System.currentTimeMillis();
                            if (currentTime - lastStatusReport > 300000) { // 5分钟
                                log("🔍 持续监控目标设备: " + getTargetDescription() + " (尚未检测到)");
                                lastStatusReport = currentTime;
                            }
                        }

                        Thread.sleep(60000); // 改为60秒检查一次
                    } catch (Exception e) {
                        log("❌ 监控线程错误: " + e.getMessage());
                        Thread.sleep(10000);
                    }
                }
            } catch (Exception e) {
                log("❌ 监控线程启动失败: " + e.getMessage());
            }
        }, "DeviceMonitor").start();
    }
    
    /**
     * 写入日志 - 简化版本
     */
    private static void log(String message) {
        String timestamp = timeFormat.format(new Date());
        String logLine = "[" + timestamp + "] " + message;

        // 输出到Xposed日志 (主要日志)
        XposedBridge.log(TAG + ": " + message);

        // 尝试写入简单日志文件 (可选，失败不影响功能)
        try {
            File simpleLog = new File(SIMPLE_LOG_FILE);
            try (FileWriter writer = new FileWriter(simpleLog, true)) {
                writer.write(logLine + "\n");
                writer.flush();
            }
        } catch (Exception e) {
            // 静默忽略文件写入错误，不影响Hook功能
        }
    }
}
