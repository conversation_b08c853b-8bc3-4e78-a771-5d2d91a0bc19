<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Lanya蓝牙距离检测模块主题 - Android 15兼容 -->
    <style name="Theme.Lanya" parent="Theme.Material3.DayNight">
        <!-- 主色调 -->
        <item name="colorPrimary">@android:color/holo_blue_dark</item>
        <item name="colorOnPrimary">@android:color/white</item>
        <item name="colorPrimaryContainer">@android:color/holo_blue_light</item>
        <item name="colorOnPrimaryContainer">@android:color/black</item>

        <!-- 次要色调 -->
        <item name="colorSecondary">@android:color/holo_green_dark</item>
        <item name="colorOnSecondary">@android:color/white</item>
        <item name="colorSecondaryContainer">@android:color/holo_green_light</item>
        <item name="colorOnSecondaryContainer">@android:color/black</item>

        <!-- 背景色 -->
        <item name="colorSurface">@android:color/background_light</item>
        <item name="colorOnSurface">@android:color/black</item>
        <item name="colorSurfaceVariant">@android:color/background_dark</item>
        <item name="colorOnSurfaceVariant">@android:color/white</item>

        <!-- 状态栏和导航栏 -->
        <item name="android:statusBarColor">@android:color/holo_blue_dark</item>
        <item name="android:navigationBarColor">@android:color/background_light</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">true</item>

        <!-- 其他属性 -->
        <item name="colorError">@android:color/holo_red_dark</item>
        <item name="colorOnError">@android:color/white</item>
        <item name="colorOutline">@android:color/darker_gray</item>
    </style>

    <!-- 保持向后兼容 -->
    <style name="Theme.BluetoothScanner" parent="Theme.Lanya" />
</resources>
