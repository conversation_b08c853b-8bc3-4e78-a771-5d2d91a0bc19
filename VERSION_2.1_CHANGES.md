# 🎯 蓝牙Hook模块 v2.1-NameSupport 更新说明

## 🆕 新增功能

### ✨ 蓝牙设备名称扫描
- **支持设备名称匹配** - 现在可以通过设备名称来监控目标设备
- **MAC地址 + 名称双重匹配** - 可以同时设置MAC地址和设备名称
- **灵活配置** - 可以只设置MAC地址、只设置设备名称，或两者都设置

### 🔧 配置方式

#### 1. 通过APP界面配置
- 打开蓝牙扫描器APP
- 点击"⚙️ 设置目标"按钮
- 可以分别设置：
  - **MAC地址** (格式: AA:BB:CC:DD:EE:FF)
  - **设备名称** (任意字符串，支持部分匹配)

#### 2. 通过配置文件
编辑 `/sdcard/bluetooth_hook_config.txt`:
```
target_mac=CF:DD:17:EC:0F
target_name=My Device
```

### 📝 匹配逻辑

1. **只设置MAC地址** - 只匹配指定MAC地址的设备
2. **只设置设备名称** - 匹配包含指定名称的所有设备
3. **同时设置两者** - 必须同时匹配MAC地址和设备名称

### 🎯 使用场景

#### 场景1: 已知MAC地址
```
target_mac=CF:DD:17:EC:0F
target_name=
```

#### 场景2: 只知道设备名称
```
target_mac=
target_name=iPhone
```

#### 场景3: 精确匹配
```
target_mac=CF:DD:17:EC:0F
target_name=John's iPhone
```

## 📱 界面改进

- **新的设置界面** - 支持同时配置MAC地址和设备名称
- **更清晰的状态显示** - 显示当前监控的目标设备信息
- **改进的日志显示** - 更好地展示目标设备信息

## 🔍 日志示例

**新版本日志:**
```
========== 极简安全蓝牙Hook启动 v2.1-NameSupport ==========
🎯 目标设备: MAC: CF:DD:17:EC:0F, 名称: iPhone
🔍 蓝牙扫描已启动，正在寻找目标设备: MAC: CF:DD:17:EC:0F, 名称: iPhone
🎯 [广播] 发现目标设备: John's iPhone (CF:DD:17:EC:0F), RSSI: -45 dBm
```

## 🚀 安装步骤

1. **运行安装脚本**:
   ```bash
   双击 install-new-version.bat
   ```

2. **配置目标设备**:
   - 打开蓝牙扫描器APP
   - 点击"⚙️ 设置目标"
   - 设置MAC地址和/或设备名称

3. **重新激活模块**:
   - 在LSPosed管理器中重新激活模块
   - 确保勾选 `com.android.bluetooth` 进程
   - 重启设备

4. **验证工作**:
   - 查看LSPosed日志
   - 确认看到 "v2.1-NameSupport" 版本标识

## 💡 使用建议

1. **推荐配置** - 同时设置MAC地址和设备名称，确保精确匹配
2. **测试方法** - 先用设备名称快速找到目标设备，再添加MAC地址精确匹配
3. **调试技巧** - 查看日志中的设备发现信息，确认目标设备的确切名称

---

**🎉 现在你可以通过设备名称轻松监控蓝牙设备了！**
