@echo off
echo ========================================
echo    修复配置文件问题
echo ========================================

echo.
echo 正在创建配置文件到多个位置...

echo # 蓝牙Hook配置文件 v2.1-NameSupport > temp_config.txt
echo # 修改配置后重启手机生效 >> temp_config.txt
echo target_mac=61:14:F1:05:88:00 >> temp_config.txt
echo target_name= >> temp_config.txt
echo # 保存时间: %date% %time% >> temp_config.txt

echo.
echo 推送配置文件到手机...
adb push temp_config.txt /sdcard/bluetooth_hook_config.txt
adb push temp_config.txt /storage/emulated/0/bluetooth_hook_config.txt
adb shell "su -c 'cp /sdcard/bluetooth_hook_config.txt /data/local/tmp/bluetooth_hook_config.txt'"

echo.
echo 删除临时文件...
del temp_config.txt

echo.
echo 安装新版本APK...
adb uninstall com.example.bluetoothscanner
adb install app\build\outputs\apk\debug\app-debug.apk

echo.
echo ========================================
echo 配置修复完成！
echo.
echo 目标MAC地址: 61:14:F1:05:88:00
echo.
echo 接下来请：
echo 1. 打开蓝牙扫描器APP
echo 2. 点击"⚙️ 设置目标"确认配置
echo 3. 在LSPosed中重新激活模块
echo 4. 重启手机
echo 5. 查看日志确认正确的MAC地址
echo ========================================

pause
