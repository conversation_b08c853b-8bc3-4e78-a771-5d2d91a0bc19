package com.example.bluetoothscanner;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/**
 * 蓝牙扫描Xposed模块入口
 */
public class XposedModule implements IXposedHookLoadPackage {
    private static final String TAG = "BluetoothHook";

    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        // Hook蓝牙进程
        if (lpparam.packageName.equals("com.android.bluetooth")) {
            XposedBridge.log(TAG + ": 🎯 开始Hook蓝牙进程: " + lpparam.packageName);
            SimpleBluetoothHook.init(lpparam.classLoader, lpparam.packageName);
        }
        // Hook自身应用进行自检
        else if (lpparam.packageName.equals("com.example.bluetoothscanner")) {
            XposedBridge.log(TAG + ": 📦 Hook自身应用进行自检: " + lpparam.packageName);
            try {
                Class<?> mainActivityClass = XposedHelpers.findClass("com.example.bluetoothscanner.MainActivity", lpparam.classLoader);
                XposedHelpers.findAndHookMethod(mainActivityClass, "isModuleActive", new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        param.setResult(true);
                        XposedBridge.log(TAG + ": ✅ 自检Hook触发 - 模块已激活!");
                    }
                });
                XposedBridge.log(TAG + ": ✅ 自检Hook安装成功");
            } catch (Exception e) {
                XposedBridge.log(TAG + ": ❌ 自检Hook安装失败: " + e.getMessage());
            }
        }
    }
}