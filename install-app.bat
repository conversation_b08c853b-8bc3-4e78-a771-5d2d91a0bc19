@echo off
echo ========================================
echo    安装蓝牙扫描Xposed模块
echo ========================================

echo.
echo APK信息:
echo 文件: app-debug.apk
echo 大小: 6.1 MB
echo 包名: com.example.bluetoothscanner
echo 目标MAC: 61:14:F1:05:88:00

echo.
echo 正在卸载旧版本...
adb uninstall com.example.bluetoothscanner

echo.
echo 正在安装新版本...
adb install app\build\outputs\apk\debug\app-debug.apk

echo.
echo 正在复制配置文件...
adb push bluetooth_hook_config.txt /sdcard/

echo.
echo ========================================
echo 安装完成！
echo.
echo 接下来请手动操作：
echo 1. 在LSPosed管理器中激活模块
echo 2. 勾选 com.android.bluetooth 进程
echo 3. 重启手机
echo 4. 打开蓝牙扫描器APP查看状态
echo 5. 查看LSPosed日志确认Hook工作
echo ========================================

pause
