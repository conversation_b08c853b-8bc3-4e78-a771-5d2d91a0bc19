<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/app_name"
            app:subtitle="@string/version" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 状态卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="系统状态"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/statusText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="🔴 需要设置"
                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/moduleStatusText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="❌ 模块未激活"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium" />

                    <TextView
                        android:id="@+id/configStatusText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="⚠️ 需要配置"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 功能按钮 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="功能操作"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="horizontal">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/configButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:text="⚙️ 配置设置"
                            app:icon="@android:drawable/ic_menu_preferences" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/testButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:text="🧪 测试功能"
                            app:icon="@android:drawable/ic_menu_search"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="使用说明"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="1. 在LSPosed管理器中激活模块\n2. 选择作用域：系统框架 (android)\n3. 重启设备使模块生效\n4. 配置目标设备和命令\n5. 模块将在后台自动工作"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabSettings"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="设置"
        app:srcCompat="@android:drawable/ic_menu_manage" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
